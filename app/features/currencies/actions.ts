import type { Currency } from "~/lib/currencies";

export const getRate = async (currencyFrom: Currency, currencyTo: Currency, date?: string | Date): Promise<number> => {
  // 1. Check if rate is in DB
  // 2. If not, fetch from API and store in DB
  // 3. Return rate
};

export const convertAmount = async (
  amount: string,
  currencyFrom: Currency,
  currencyTo: Currency,
  date?: string | Date
): Promise<string> => {
  // 1. Get currency rate
  // 2. Convert amount
  // 3. Return converted amount
};
