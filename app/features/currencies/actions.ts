import type { Currency } from "~/lib/currencies";

import { formatISO, isToday } from "date-fns";
import { Decimal } from "decimal.js";
import { and, eq, sql } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";

import db from "~/db";
import { currencyRates } from "~/db/schemas";
import { currencies } from "~/lib/currencies";
import logger from "~/lib/logger";
import { StatusCodes } from "~/lib/status-codes";

import ratesProvider from "./providers";

/**
 * Gets exchange rate from database
 */
const getRateFromDB = async (
  currencyFrom: Currency,
  currencyTo: Currency,
  rateDate: string
): Promise<string | null> => {
  const [rate] = await db
    .select()
    .from(currencyRates)
    .where(
      and(
        eq(currencyRates.currencyFrom, currencyFrom),
        eq(currencyRates.currencyTo, currencyTo),
        eq(currencyRates.rateDate, rateDate)
      )
    );

  return rate?.rate ?? null;
};

/**
 * Stores exchange rate in database
 */
const storeRateInDB = async (
  currencyFrom: Currency,
  currencyTo: Currency,
  rateDate: string,
  rate: string
): Promise<void> => {
  await db
    .insert(currencyRates)
    .values({
      currencyFrom,
      currencyTo,
      rateDate,
      rate,
    })
    .onConflictDoUpdate({
      target: [currencyRates.currencyFrom, currencyRates.currencyTo, currencyRates.rateDate],
      set: {
        rate,
        updatedAt: new Date(),
      },
    });
};

/**
 * Fetches exchange rate from API provider
 */
const fetchRateFromAPI = async (currencyFrom: Currency, currencyTo: Currency, rateDate: string): Promise<string> => {
  try {
    let response;

    if (isToday(rateDate)) {
      // Fetch latest rates for today
      response = await ratesProvider.latest({
        base_currency: currencyFrom,
        currencies: currencyTo,
      });
    } else {
      // Fetch historical rates for specific date
      response = await ratesProvider.historical({
        date: rateDate,
        base_currency: currencyFrom,
        currencies: currencyTo,
      });
    }

    const rate = response.data[currencyTo]?.value;
    if (rate === undefined || rate === 0) {
      logger.error({ currencyFrom, currencyTo, rateDate, response }, "Exchange rate not available");
      throw new HTTPException(StatusCodes.BAD_REQUEST, {
        message: `Exchange rate not available for ${currencyFrom} to ${currencyTo} on ${rateDate}`,
      });
    }

    return new Decimal(rate).toFixed(4);
  } catch (error) {
    logger.error({ currencyFrom, currencyTo, rateDate, error }, "Failed to fetch exchange rate");
    if (error instanceof HTTPException) {
      throw error;
    }
    throw new HTTPException(StatusCodes.INTERNAL_SERVER_ERROR, {
      message: `Failed to fetch exchange rate: ${error instanceof Error ? error.message : "Unknown error"}`,
    });
  }
};

export const getRate = async (currencyFrom: Currency, currencyTo: Currency, date?: string | Date): Promise<string> => {
  // Handle same currency conversion
  if (currencyFrom === currencyTo) {
    return "1.0000";
  }

  // Format the date
  const rateDate = formatISO(date ?? new Date(), { representation: "date" });

  // 1. Check if rate is in DB
  const existingRate = await getRateFromDB(currencyFrom, currencyTo, rateDate);
  if (existingRate !== null) {
    return existingRate;
  }

  // 2. Fetch from API and store in DB
  const rate = await fetchRateFromAPI(currencyFrom, currencyTo, rateDate);
  await storeRateInDB(currencyFrom, currencyTo, rateDate, rate);

  return rate;
};

export const convertAmount = async (
  amount: string,
  currencyFrom: Currency,
  currencyTo: Currency,
  date?: string | Date
): Promise<string> => {
  // Validate amount
  const amountDecimal = new Decimal(amount);
  if (amountDecimal.isNaN() || amountDecimal.isNegative()) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, {
      message: "Amount must be a valid positive number",
    });
  }

  // Handle same currency conversion
  if (currencyFrom === currencyTo) {
    return amountDecimal.toFixed(4);
  }

  // 1. Get currency rate
  const rate = await getRate(currencyFrom, currencyTo, date);

  // 2. Convert amount using Decimal.js to preserve precision
  const rateDecimal = new Decimal(rate);
  const convertedAmount = amountDecimal.mul(rateDecimal);

  // 3. Return converted amount as string with 4 decimal places
  return convertedAmount.toFixed(4);
};

/**
 * Stores multiple exchange rates in database using batch insert
 */
const batchStoreRatesInDB = async (
  rates: Array<{
    currencyFrom: Currency;
    currencyTo: Currency;
    rateDate: string;
    rate: string;
  }>
): Promise<void> => {
  if (rates.length === 0) {
    return;
  }

  logger.info({ count: rates.length }, "Storing rates in database");

  await db
    .insert(currencyRates)
    .values(rates)
    .onConflictDoUpdate({
      target: [currencyRates.currencyFrom, currencyRates.currencyTo, currencyRates.rateDate],
      set: {
        rate: sql.raw(`excluded.${currencyRates.rate.name}`),
        updatedAt: new Date(),
      },
    });

  logger.info({ count: rates.length }, "Successfully stored rates in database");
};

/**
 * Updates exchange rates for all supported currencies
 * Fetches latest rates from API and stores them in database using batch operations
 */
export const updateAllRates = async (): Promise<void> => {
  const today = formatISO(new Date(), { representation: "date" });
  const ratesToStore: Array<{
    currencyFrom: Currency;
    currencyTo: Currency;
    rateDate: string;
    rate: string;
  }> = [];

  logger.info({ currencies, date: today }, "Starting to update all currency rates");

  try {
    // For each currency as base, fetch rates to all other currencies
    for (const baseCurrency of currencies) {
      logger.debug({ baseCurrency }, "Fetching rates for base currency");

      try {
        // Fetch latest rates from API
        const response = await ratesProvider.latest({
          base_currency: baseCurrency,
          currencies: currencies.join(","),
        });

        logger.debug(
          {
            baseCurrency,
            currencies,
            responseData: response.data,
            lastUpdated: response.meta.last_updated_at,
          },
          "Received rates from API"
        );

        // Process each rate from the response
        for (const targetCurrency of currencies) {
          const rateData = response.data[targetCurrency];

          if (rateData?.value !== undefined && rateData.value > 0) {
            const rate = new Decimal(rateData.value).toFixed(4);

            ratesToStore.push({
              currencyFrom: baseCurrency,
              currencyTo: targetCurrency,
              rateDate: today,
              rate,
            });
          } else {
            logger.warn({ baseCurrency, targetCurrency, rateData }, "Invalid or missing rate data");
          }
        }

        // Add a small delay between API calls to be respectful to the API
        await new Promise((resolve) => setTimeout(resolve, 100));
      } catch (error) {
        logger.error(
          { baseCurrency, error: error instanceof Error ? error.message : error },
          "Failed to fetch rates for base currency"
        );
        // Continue with other currencies even if one fails
      }
    }

    // Store all rates in database using batch insert
    if (ratesToStore.length > 0) {
      await batchStoreRatesInDB(ratesToStore);

      logger.info(
        {
          totalRates: ratesToStore.length,
          uniquePairs: new Set(ratesToStore.map((r) => `${r.currencyFrom}-${r.currencyTo}`)).size,
          date: today,
        },
        "Successfully updated all currency rates"
      );
    } else {
      logger.warn("No rates were fetched from API");
    }
  } catch (error) {
    logger.error({ error: error instanceof Error ? error.message : error }, "Failed to update currency rates");
    throw error;
  }
};
