import { <PERSON>ala<PERSON> } from "@scalar/hono-api-reference";
import { every, except } from "hono/combine";
import { jwt } from "hono/jwt";
import { prettyJSON } from "hono/pretty-json";
import { requestId } from "hono/request-id";

import errorHandler from "./common/middleware/error-handler";
import loggerMiddleware from "./common/middleware/logger";
import notFound from "./common/middleware/not-found";
import { addApiV1Routes } from "./features/router";
import { currentUser } from "./features/users/middleware";
import env from "./lib/env";
import createRouter from "./lib/router";

const app = createRouter();

app
  .use(requestId())
  .use(loggerMiddleware())
  .use(prettyJSON({ space: 2 }));

app.notFound(notFound);
app.onError(errorHandler);

// configure OpenAPI
app.doc31("/api/openapi.json", {
  openapi: "3.1.0",
  info: {
    title: "Finanze.Pro API",
    description: "API for Finanze.Pro",
    version: "1.0.0",
  },
});
app.get(
  "/api/docs",
  Scalar({
    pageTitle: "Finanze.Pro API",
    layout: "modern",
    defaultHttpClient: {
      targetKey: "js",
      clientKey: "fetch",
    },
    url: "/api/openapi.json",
    generateOperationSlug: (operation) => operation.operationId ?? `${operation.method}${operation.path}`,
  })
);

app.openAPIRegistry.registerComponent("securitySchemes", "JWT", {
  type: "http",
  scheme: "bearer",
  bearerFormat: "JWT",
});

// security settings and application routes
app.use(
  "/api/*",
  except(
    (c) => {
      if (["/api/docs", "/api/openapi.json", "/api/v1/auth/token"].includes(c.req.path)) {
        return true;
      }

      if (c.req.path === "/api/v1/users" && c.req.method === "POST") {
        return true;
      }

      return false;
    },
    every(jwt({ secret: env.JWT_SECRET, cookie: "access_token" }), currentUser())
  )
);

addApiV1Routes(app);

export default app;
