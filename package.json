{"name": "backend", "description": "Finanze.Pro Backend API", "module": "app/index.ts", "type": "module", "private": true, "scripts": {"dev": "bun run --watch app/index.ts", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write ."}, "dependencies": {"@asteasolutions/zod-to-openapi": "^7.3.2", "@hono/zod-openapi": "^0.19.8", "@scalar/hono-api-reference": "^0.9.1", "dotenv": "^16.5.0", "dotenv-expand": "^12.0.2", "drizzle-orm": "^0.44.0", "drizzle-zod": "^0.8.2", "hono": "^4.7.10", "hono-pino": "^0.8.0", "pg": "^8.16.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "zod": "^3.25.42"}, "devDependencies": {"@eslint/js": "^9.27.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.2", "@types/bun": "latest", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "drizzle-kit": "^0.31.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "globals": "^16.2.0", "prettier": "^3.5.3"}, "peerDependencies": {"typescript": "^5"}}